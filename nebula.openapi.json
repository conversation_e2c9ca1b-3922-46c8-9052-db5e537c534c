{"openapi": "3.0.1", "info": {"title": "nebula", "description": "中一检测BFF服务", "version": "1.0.0"}, "tags": [{"name": "文档发放管理"}], "paths": {"/nebula/api/v1/document-library/document/distribute/user-permissions": {"get": {"summary": "根据文档 id 查询发放回收用户记录", "deprecated": false, "description": "根据发放记录 ID 查询发放详情信息，包括基本信息、发放清单、权限详情等完整信息", "operationId": "getDistributeDetail", "tags": ["文档发放管理"], "parameters": [{"name": "documentId", "in": "query", "description": "发放记录ID", "required": true, "example": "29741498243488", "schema": {"type": "string", "example": "distribute-123456"}}, {"name": "Authorization", "in": "header", "description": "", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VLaW5kIjoid2ViIiwiZXhwIjoxNzUyMDY3Mzk3LCJpYXQiOjE3NTIwNTI5OTcsImlzQWRtaW5Vbml0IjpmYWxzZSwiaXNTdXBlckFkbWluIjpmYWxzZSwiaXNWaXJ0dWFsVXNlciI6ZmFsc2UsIm1vYmlsZSI6IjE5OTA2NTQwNDE0Iiwib3JnYW5pemF0aW9uSWQiOiI1Njk3MTg5MDI4Mjk5MDM1MTAiLCJ0ZW5hbnRJZCI6IjUxMjU4MjIzMzA4MTY3MTE4NiIsInVzZXJJZCI6IjU3MDg0NTE5MzcyOTc2NTAxNCJ9.-4uVT4XIS8u2bnubvTbn4OrhEMjGQQS3KVW5d6Cucgs", "schema": {"type": "string", "default": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VLaW5kIjoid2ViIiwiZXhwIjoxNzUyMDY3Mzk3LCJpYXQiOjE3NTIwNTI5OTcsImlzQWRtaW5Vbml0IjpmYWxzZSwiaXNTdXBlckFkbWluIjpmYWxzZSwiaXNWaXJ0dWFsVXNlciI6ZmFsc2UsIm1vYmlsZSI6IjE5OTA2NTQwNDE0Iiwib3JnYW5pemF0aW9uSWQiOiI1Njk3MTg5MDI4Mjk5MDM1MTAiLCJ0ZW5hbnRJZCI6IjUxMjU4MjIzMzA4MTY3MTE4NiIsInVzZXJJZCI6IjU3MDg0NTE5MzcyOTc2NTAxNCJ9.-4uVT4XIS8u2bnubvTbn4OrhEMjGQQS3KVW5d6Cucgs"}}, {"name": "X-Session-User-Id", "in": "header", "description": "", "example": "571276955802955414", "schema": {"type": "string", "default": "571276955802955414"}}, {"name": "X-Session-Tenant-Id", "in": "header", "description": "", "example": "575527573568777878", "schema": {"type": "string", "default": "575527573568777878"}}, {"name": "X-Session-Organization-Id", "in": "header", "description": "", "example": "569718902829903510", "schema": {"type": "string", "default": "569718902829903510"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码，0表示成功"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"inventoryId": {"type": "string", "description": "发放清单 id"}, "fileForm": {"type": "string", "description": "文件形式,1电子文件 | 2纸质文件"}, "filePermission": {"type": "string", "description": "文件权限,1查阅 | 2查阅/下载 | 3一次下载"}, "userId": {"type": "string", "description": "用户id"}, "userNickName": {"type": "integer", "description": "用户昵称"}, "status": {"type": "integer", "description": "状态 1发放审批中  | 2已发放  |  3 回收审批人"}}}}}, "required": ["list"]}}, "required": ["code", "message", "data"]}, "example": {"code": 89, "message": "dolor in ut tempor qui", "data": {"list": [{"inventoryId": "44", "fileForm": "ea dolor dolore", "filePermission": "nostrud", "userId": "48", "userNickName": 13, "status": 89}, {"inventoryId": "56", "fileForm": "ut ipsum incididunt dolore aliqua", "filePermission": "proident Excepteur aute reprehenderit", "userId": "32", "userNickName": 44, "status": 85}, {"inventoryId": "43", "fileForm": "aliqua veniam sed", "filePermission": "eu enim tempor pariatur aute", "userId": "28", "userNickName": 34, "status": 46}]}}}}, "headers": {}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 400, "message": "参数错误", "details": "缺少必填参数"}}}, "headers": {}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 401, "message": "未授权", "details": "请提供有效的认证信息"}}}, "headers": {}}, "404": {"description": "资源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 404, "message": "资源不存在", "details": "指定的发放记录不存在"}}}, "headers": {}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 500, "message": "服务器内部错误", "details": "系统异常，请稍后重试"}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {"ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误代码", "example": 400}, "message": {"type": "string", "description": "错误信息", "example": "参数错误"}, "details": {"type": "string", "description": "详细错误信息", "example": "缺少必填参数 id"}}}}, "securitySchemes": {}}, "servers": [], "security": []}