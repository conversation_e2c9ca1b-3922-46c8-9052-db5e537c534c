package document_library

import (
	"context"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLoanRecordDocumentsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLoanRecordDocumentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLoanRecordDocumentsLogic {
	return &GetLoanRecordDocumentsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLoanRecordDocumentsLogic) GetLoanRecordDocuments(req *types.GetLoanRecordDocumentsReq) (resp *types.GetLoanRecordDocumentsResp, err error) {
	// todo: add your logic here and delete this line

	return
}
