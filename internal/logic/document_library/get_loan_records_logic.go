package document_library

import (
	"context"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLoanRecordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLoanRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLoanRecordsLogic {
	return &GetLoanRecordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLoanRecordsLogic) GetLoanRecords(req *types.GetLoanRecordsReq) (resp *types.GetLoanRecordsResp, err error) {
	// todo: add your logic here and delete this line

	return
}
