package mapper

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	TableNameDisposalRecord = "disposal_records"
)

// DisposalRecord 对应 disposal_records 表
type DisposalRecord struct {
	ID                 string         `gorm:"type:varchar(64);primary_key"`
	DistributeRecordID string         `gorm:"type:varchar(64);index;comment:'关联的发放记录ID'"`
	DisposalBy         string         `gorm:"type:varchar(64);comment:'处置人,用户id'"`
	DisposalDate       time.Time      `gorm:"comment:'处置日期'"`
	Reason             string         `gorm:"type:text;comment:'处置方式'"`
	WorkflowID         string         `gorm:"type:varchar(64);comment:'流程ID'"`
	ApprovalInfo       datatypes.JSON `gorm:"type:json;column:approval_info;comment:'审批信息'"`
	CreatedAt          time.Time      `gorm:"column:created_at"` // 创建时间
	UpdatedAt          time.Time      `gorm:"column:updated_at"` // 更新时间
	CreatedBy          string         `gorm:"type:varchar(64);column:created_by"`
	UpdatedBy          string         `gorm:"type:varchar(64);column:updated_by"`
}

func (DisposalRecord) TableName() string {
	return TableNameDisposalRecord
}

// DisposalRecordClient 是 disposal_records 表的数据访问客户端
type DisposalRecordClient struct {
	db *gorm.DB
}

// NewDisposalRecordClient 创建一个新的 DisposalRecordClient 实例
func NewDisposalRecordClient(db *DocvaultDB) *DisposalRecordClient {
	return &DisposalRecordClient{
		db: db.GetDB(),
	}
}

// GetDisposalDetailByDistributeRecordFileID 根据发放清单文件ID查询处置详情
// 参数:
//   - ctx: 上下文对象，用于控制请求的生命周期和传递请求范围的值
//   - distributeRecordFileID: 发放清单文件ID，用于关联查询处置记录
//
// 返回值:
//   - fileName: 文件名称
//   - fileNumber: 文件编号
//   - disposalRecords: 处置记录列表，包含交还人、回收人、处置人等信息
//   - err: 错误信息，如果查询失败则返回相应错误
//
// 功能说明:
//   - 通过发放清单文件ID关联查询相关的处置记录
//   - 同时查询文件的基本信息（文件名称、文件编号）
//   - 查询处置记录的详细信息，包括交还、回收、处置各个环节的人员和时间
//   - 支持一个发放清单文件对应多条处置记录的场景
//   - 返回完整的处置流程信息，便于业务层进行数据展示
func (c *DisposalRecordClient) GetDisposalDetailByDistributeRecordFileID(ctx context.Context, distributeRecordFileID string) (fileName, fileNumber string, disposalRecords []DisposalRecordDetail, err error) {
	// 构建查询SQL，关联查询发放清单文件、回收记录、处置记录等相关表
	// 查询逻辑：
	// 1. 从distribute_record_files表获取文件基本信息
	// 2. 关联recycle_records表获取回收信息
	// 3. 关联disposal_records表获取处置信息
	// 4. 关联recycle_record_permissions和disposal_record_permissions获取权限信息
	query := `
		SELECT DISTINCT
			drf.file_name,
			drf.number as file_number,
			COALESCE(rr.recycle_by, '') as recycle_person,
			COALESCE(rr.recycle_date, '1970-01-01 00:00:00') as recycle_date,
			COALESCE(dr.disposal_by, '') as disposal_person,
			COALESCE(dr.disposal_date, '1970-01-01 00:00:00') as disposal_date,
			COALESCE(dr.reason, '') as disposal_method,
			COALESCE(rrp.user_id, '') as handover_person,
			COALESCE(rr.created_at, '1970-01-01 00:00:00') as handover_date
		FROM distribute_record_files drf
		LEFT JOIN recycle_record_files rrf ON drf.id = rrf.distribute_record_file_id
		LEFT JOIN recycle_records rr ON rrf.record_id = rr.id
		LEFT JOIN recycle_record_permissions rrp ON rrf.id = rrp.file_record_id
		LEFT JOIN disposal_record_files drf2 ON drf.id = drf2.distribute_record_file_id
		LEFT JOIN disposal_records dr ON drf2.record_id = dr.id
		WHERE drf.id = ?
		ORDER BY rr.recycle_date DESC, dr.disposal_date DESC
	`

	rows, err := c.db.WithContext(ctx).Raw(query, distributeRecordFileID).Rows()
	if err != nil {
		return "", "", nil, err
	}
	defer rows.Close()

	var records []DisposalRecordDetail
	var currentFileName, currentFileNumber string

	for rows.Next() {
		var record DisposalRecordDetail
		var recycleDate, disposalDate, handoverDate time.Time

		err := rows.Scan(
			&currentFileName,
			&currentFileNumber,
			&record.RecyclePerson,
			&recycleDate,
			&record.DisposalPerson,
			&disposalDate,
			&record.DisposalMethod,
			&record.HandoverPerson,
			&handoverDate,
		)
		if err != nil {
			return "", "", nil, err
		}

		// 转换时间为毫秒级时间戳
		record.RecycleDate = recycleDate.UnixMilli()
		record.DisposalDate = disposalDate.UnixMilli()
		record.HandoverDate = handoverDate.UnixMilli()

		records = append(records, record)
	}

	if err = rows.Err(); err != nil {
		return "", "", nil, err
	}

	return currentFileName, currentFileNumber, records, nil
}

// DisposalRecordDetail 处置记录详情结构体
// 用于封装处置流程中的完整信息，包括交还、回收、处置三个环节
type DisposalRecordDetail struct {
	HandoverPerson string `json:"handoverPerson"` // 交还人ID
	HandoverDate   int64  `json:"handoverDate"`   // 交还日期（毫秒级时间戳）
	RecyclePerson  string `json:"recyclePerson"`  // 回收人ID
	RecycleDate    int64  `json:"recycleDate"`    // 回收日期（毫秒级时间戳）
	DisposalPerson string `json:"disposalPerson"` // 处置人ID
	DisposalDate   int64  `json:"disposalDate"`   // 处置日期（毫秒级时间戳）
	DisposalMethod string `json:"disposalMethod"` // 处置方式描述
}
