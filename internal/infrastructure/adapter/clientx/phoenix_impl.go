package clientx

import (
	"context"
	"errors"
	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/clientx/entity"
	"nebula/internal/utils"
	"time"

	"io"
	"net/http"
	"os"
	"path/filepath"

	"gitee.com/damengde/teemo"
	"github.com/zeromicro/go-zero/core/logc"
)

type PhoenixClientImpl struct {
	cl   *teemo.Client
	conf config.Config
}

func NewPhoenixClientImpl(conf config.Config) PhoenixClient {
	service := conf.MicroServices.GetService("phoenix")
	if service.Url == "" {
		return nil
	}
	cl := teemo.New(teemo.WithBaseURL(service.Url), teemo.WithTimeout(time.Second*20))
	return &PhoenixClientImpl{
		cl:   cl,
		conf: conf,
	}
}

func (p *PhoenixClientImpl) GetUserNicknames(ctx context.Context, uids []string) (map[string]string, error) {
	respBody, err := teemo.NewRequest[entity.GetUserNicknamesResp](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Post(ctx, "/saas/api/v1/user/nicknames/byids", map[string]interface{}{
			"ids": uids,
		})
	if err != nil {
		return nil, err
	}
	if err := p.checkResult(ctx, respBody.CommonResp); err != nil {
		return nil, err
	}

	return respBody.Data, nil
}

func (p *PhoenixClientImpl) GetFileInfo(ctx context.Context, fileId string) (entity.FileInfo, error) {
	respBody, err := teemo.NewRequest[entity.GetFileInfoResp](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Get(ctx, "/file/api/v1/file/get/"+fileId, nil)
	if err != nil {
		return entity.FileInfo{}, err
	}
	if err := p.checkResult(ctx, respBody.CommonResp); err != nil {
		return entity.FileInfo{}, err
	}
	return respBody.Data, nil
}

func (p *PhoenixClientImpl) GetWorkflow(ctx context.Context, workflowId string) (entity.GetWorkflowRespData, error) {
	respBody, err := teemo.NewRequest[entity.GetWorkflowResp](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Get(ctx, "/saas/api/v1/workflow", map[string]interface{}{
			"flowId": workflowId,
		})
	if err != nil {
		return entity.GetWorkflowRespData{}, err
	}
	if err := p.checkResult(ctx, respBody.CommonResp); err != nil {
		return entity.GetWorkflowRespData{}, err
	}

	return respBody.Data, nil
}
func (p *PhoenixClientImpl) checkResult(ctx context.Context, resp entity.CommonResp) error {
	if resp.Code != 0 {
		logc.Errorf(ctx, "phoenix request fail, response code:%d msg:%s", resp.Code, resp.Msg)
		return errors.New(resp.Msg)
	}
	return nil
}

func (p *PhoenixClientImpl) getHeaders(ctx context.Context) map[string]string {
	return map[string]string{
		"X-Session-User-Id":         utils.GetContextUserID(ctx),
		"X-Session-Tenant-Id":       utils.GetContextTenantID(ctx),
		"X-Session-Organization-Id": utils.GetContextOrganizationID(ctx),
	}
}

func (p *PhoenixClientImpl) GetOrganizationInfo(ctx context.Context, orgId string) (organizationInfo entity.OrganizationInfo, err error) {
	respBody, err := teemo.NewRequest[entity.GetOrganizationInfoResp](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Get(ctx, "/saas/api/v1/organization/"+orgId, nil)
	if err != nil {
		return entity.OrganizationInfo{}, err
	}
	if err := p.checkResult(ctx, respBody.CommonResp); err != nil {
		return entity.OrganizationInfo{}, err
	}
	return respBody.Data, nil
}

func (p *PhoenixClientImpl) GetAllOrganizationsAndUsersByOrgId(ctx context.Context, orgID string) (resp entity.OrganizationUserTree, err error) {
	respBody, err := teemo.NewRequest[entity.OrganizationUserTree](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Get(ctx, "/saas/api/v1/organization_user/list/tree", map[string]interface{}{
			"organizationId": orgID,
		})
	if err != nil {
		return entity.OrganizationUserTree{}, err
	}
	if err := p.checkResult(ctx, respBody.CommonResp); err != nil {
		return entity.OrganizationUserTree{}, err
	}
	return respBody, nil
}

func (p *PhoenixClientImpl) GetOrganizationInfoByCodes(ctx context.Context, codes []string) ([]entity.OrganizationInfo, error) {
	respBody, err := teemo.NewRequest[entity.GetOrganizationInfoByCodesResp](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Post(ctx, "/saas/api/v1/organization/list/bycodes", map[string]interface{}{
			"codes": codes,
		})
	if err != nil {
		return nil, err
	}
	if err := p.checkResult(ctx, respBody.CommonResp); err != nil {
		return nil, err
	}

	return respBody.Data, nil
}

func (p *PhoenixClientImpl) GetUserInfoByMobiles(ctx context.Context, mobiles []string) ([]entity.UserInfo, error) {
	respBody, err := teemo.NewRequest[entity.GetUserInfoByMobilesResp](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Post(ctx, "/saas/api/v1/user/list/bymobiles", map[string]interface{}{
			"mobiles": mobiles,
		})
	if err != nil {
		return nil, err
	}
	if err := p.checkResult(ctx, respBody.CommonResp); err != nil {
		return nil, err
	}
	return respBody.Data, nil
}

// UploadFile 根据本地文件路径上传文件，可指定文件名，返回 fileId 和错误信息
func (p *PhoenixClientImpl) UploadFile(ctx context.Context, filePath, fileName string) (string, error) {
	// 1. 获取文件名和大小
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return "", err
	}
	if fileName == "" {
		fileName = filepath.Base(filePath)
	}
	fileSize := fileInfo.Size()

	// 2. 获取预签名URL和fileId
	uploadUrl, fileId, err := p.getFileUploadPresignedURL(ctx, fileName, fileSize)
	if err != nil {
		return "", err
	}

	// 3. 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 4. PUT 上传
	req, err := http.NewRequestWithContext(ctx, http.MethodPut, uploadUrl, file)
	if err != nil {
		return "", err
	}
	req.ContentLength = fileSize

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		return "", errors.New("上传失败: " + string(body))
	}

	return fileId, nil
}

// getFileUploadPresignedURL 获取文件上传预签名URL（私有方法）
func (p *PhoenixClientImpl) getFileUploadPresignedURL(ctx context.Context, fileName string, fileSize int64) (string, string, error) {
	type presignedReq struct {
		FileName string `json:"fileName"`
		FileSize int64  `json:"fileSize"`
	}
	type presignedResp struct {
		ID                     string `json:"id"`
		FileUploadPreSignedUrl string `json:"fileUploadPreSignedUrl"`
	}
	type apiResp struct {
		Code int           `json:"code"`
		Msg  string        `json:"msg"`
		Data presignedResp `json:"data"`
	}

	req := presignedReq{
		FileName: fileName,
		FileSize: fileSize,
	}
	respBody, err := teemo.NewRequest[apiResp](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Post(ctx, "/file/api/v1/file/upload/presignedurl/generate", req)
	if err != nil {
		return "", "", err
	}
	if respBody.Code != 0 {
		return "", "", errors.New(respBody.Msg)
	}
	return respBody.Data.FileUploadPreSignedUrl, respBody.Data.ID, nil
}

func (p *PhoenixClientImpl) GetUserInfoByNickname(ctx context.Context, nickname string) (resp []entity.UserInfo, err error) {
	respBody, err := teemo.NewRequest[entity.GetUserInfoByNicknameResp](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Get(ctx, "/saas/api/v1/user/list/bynickname", map[string]interface{}{
			"nickname": nickname,
		})
	if err != nil {
		return nil, err
	}
	if err = p.checkResult(ctx, respBody.CommonResp); err != nil {
		return nil, err
	}
	return respBody.Data.Data, nil
}

// CheckUserHasRoleCode 检查用户是否包含指定角色代码
func (p *PhoenixClientImpl) CheckUserHasRoleCode(ctx context.Context, userId, roleCode string) (bool, error) {
	respBody, err := teemo.NewRequest[entity.CheckUserHasRoleCodeResp](p.cl).
		SetHeaders(p.getHeaders(ctx)).
		Get(ctx, "/saas/api/v1/role/check_user_has_role_code", map[string]interface{}{
			"userId":   userId,
			"roleCode": roleCode,
		})
	if err != nil {
		logc.Errorf(ctx, "检查用户角色失败: %v", err)
		return false, err
	}
	if err := p.checkResult(ctx, respBody.CommonResp); err != nil {
		logc.Errorf(ctx, "检查用户角色响应错误: %v", err)
		return false, err
	}

	return respBody.Data, nil
}
